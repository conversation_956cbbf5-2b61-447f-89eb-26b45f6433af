CREATE DEFINER=`root`@`%` PROCEDURE `rms_fx_yuliu_med`(
		IN p_code VARCHAR(50),
    IN p_yp_code VARCHAR(60)
)
    COMMENT '药品余留/用药总量分析存储过程'
main_block: BEGIN
		-- 声明变量
		DECLARE v_sda_id VARCHAR(20);
		DECLARE v_yp_name VARCHAR(200);
		DECLARE v_yp_name2 VARCHAR(200);
		DECLARE v_yp_yyzl VARCHAR(50);    -- 用户自定义的用药总量最大值
		DECLARE v_ord_qty VARCHAR(50);    -- his传来的用药总量值
		DECLARE v_ord_uom VARCHAR(50);
		DECLARE v_n_count2 INT;
		DECLARE v_n_count11 INT;
		DECLARE v_ks VARCHAR(50);
		DECLARE v_hosp_flag VARCHAR(50);
		DECLARE v_value DECIMAL(10,2);
		DECLARE v_len_value INT;
		DECLARE v_pres_type2 VARCHAR(50);
		DECLARE v_n_count6 INT;
		DECLARE v_mid VARCHAR(50);
		DECLARE v_ksdm VARCHAR(20);
		DECLARE v_n_count INT;
		DECLARE v_n_count1 INT;
		DECLARE v_ysdm VARCHAR(20);
		DECLARE v_n_count5 INT;
		DECLARE v_n_count3 INT;
		DECLARE v_n_count36 INT;
		DECLARE v_n_count9 INT;
		DECLARE v_n_count10 INT;
		DECLARE v_n_count12 INT;
		DECLARE v_n_count13 INT;
		DECLARE v_n_count18 INT;
		DECLARE v_n_count19 INT;
		DECLARE v_tymc VARCHAR(50);
		DECLARE v_n_count20 INT;
		DECLARE v_n_count21 INT;
		DECLARE v_n_count22 INT;
		DECLARE v_n_count23 INT;
		DECLARE v_DAILY_TIMES INT;
		DECLARE v_yl_min INT;
		DECLARE v_ts_int INT;
		DECLARE v_zdts DECIMAL(14,2);
		DECLARE v_sj_zdts DECIMAL(14,2);
		DECLARE v_sdate VARCHAR(50);
		DECLARE v_his_time VARCHAR(50);
		DECLARE v_his_time2 VARCHAR(50);
		DECLARE v_last_his_time VARCHAR(50);
		DECLARE v_last_code VARCHAR(50);
		DECLARE v_dept_name VARCHAR(50);
		DECLARE v_doct_name VARCHAR(50);
		DECLARE v_n_count24 INT;
		DECLARE v_card_code VARCHAR(50);
		DECLARE v_pres_id VARCHAR(50);

		-- 声明异常处理
		DECLARE CONTINUE HANDLER FOR SQLEXCEPTION
		BEGIN
				-- 如果出现异常，直接返回
				ROLLBACK;
				GET DIAGNOSTICS CONDITION 1
            @sqlstate = RETURNED_SQLSTATE,
            @errno = MYSQL_ERRNO,
            @text = MESSAGE_TEXT;

				select CONCAT('执行异常: ', @sqlstate, @errno, @text);
		END;

		-- 检查药品是否为停用状态
		SELECT COUNT(1) INTO v_n_count6
		FROM rms_itf_hos_drug b
		WHERE b.DRUG_CODE = p_yp_code AND b.ZX_FLAG = '3';

		IF v_n_count6 > 0 THEN
				LEAVE main_block;
		END IF;

		-- 获取药品基本信息
		SELECT sda_id, cid INTO v_sda_id, v_mid
		FROM rms_t_byyydzb
		WHERE yp_code = p_yp_code LIMIT 1;

		-- 获取处方药品信息
		SELECT med_name, ord_qty, ord_uom INTO v_yp_name, v_ord_qty, v_ord_uom
		FROM rms_t_pres_med
		WHERE code = p_code AND his_code = p_yp_code LIMIT 1;

		-- 获取药品最大发药量
		SELECT zdfyl INTO v_yp_yyzl
		FROM rms_t_drug_zdfyl
		WHERE drug_code = p_yp_code AND unit = v_ord_uom LIMIT 1;

		-- 获取处方类型信息
		SELECT hosp_flag, pres_type INTO v_hosp_flag, v_pres_type2
		FROM rms_t_pres
		WHERE Code = p_code LIMIT 1;

		-- 门诊用药总量检查
		IF v_hosp_flag = 'op' AND (v_yp_yyzl != '' AND v_yp_yyzl IS NOT NULL) THEN
				SET v_value = CAST(v_yp_yyzl AS DECIMAL(10,2)) - CAST(v_ord_qty AS DECIMAL(10,2));
				IF v_value < 0 THEN
						INSERT INTO rms_t_pres_fx
						SELECT p_code, v_yp_name, '', '1', '一般提示', 'RLT047', 'yyzlwt', '用药总量', '用药总量问题',
								CONCAT(v_yp_name, '超出最大发药量,医院要求门诊发药最大量为', v_yp_yyzl, v_ord_uom), '0', '用药总量';
				END IF;
		END IF;

		-- 外用软膏最大发药量检查（不能超过5支）
		SELECT COUNT(1) INTO v_n_count36
		FROM rms_itf_hos_drug b
		WHERE b.DRUG_CODE = p_yp_code
		AND b.DRUG_FOR_NAME IN ('软膏剂', '乳膏剂', '凝胶(胶浆)剂', '眼膏剂', '茶剂', '滴眼剂');

		IF v_hosp_flag = 'op' AND v_n_count36 > 0 AND CAST(v_ord_qty AS DECIMAL(10,2)) > 5 THEN
				INSERT INTO rms_t_pres_fx
				SELECT p_code, v_yp_name, '', '1', '一般提示', 'RLT047', 'yyzlwt', '用药总量', '用药总量问题',
						CONCAT(v_yp_name, '超出最大发药量,医院要求门诊软膏、滴眼剂发药最大量为5支'), '0', '用药总量';
		END IF;

		-- 药品自定义科室检查
		SELECT COUNT(1) INTO v_n_count
		FROM rms_t_med_zdy_dept
		WHERE yp_code = p_yp_code;

		IF v_n_count > 0 THEN
				SELECT dept_code INTO v_ksdm
				FROM rms_t_pres
				WHERE code = p_code LIMIT 1;

				SELECT COUNT(1) INTO v_n_count1
				FROM rms_t_med_zdy_dept
				WHERE yp_code = p_yp_code AND dept_code = v_ksdm;

				IF v_n_count1 = 0 THEN
						INSERT INTO rms_t_pres_fx
						SELECT p_code, v_yp_name, '', '1', '严重警示', 'RLT048', 'yyks', '用药科室', '用药科室问题',
								CONCAT('医院规定：', v_yp_name, '不能在该科室使用！'), '0', '用药科室';
				END IF;
		END IF;

		-- 药品自定义医生检查
		SELECT COUNT(1) INTO v_n_count5
		FROM rms_t_med_zdy_doct
		WHERE yp_code = p_yp_code;

		IF v_n_count5 > 0 THEN
				SELECT doct_code INTO v_ysdm
				FROM rms_t_pres
				WHERE code = p_code LIMIT 1;

				SELECT COUNT(1) INTO v_n_count3
				FROM rms_t_med_zdy_doct
				WHERE yp_code = p_yp_code AND doct_code = v_ysdm;

				IF v_n_count3 = 0 THEN
						INSERT INTO rms_t_pres_fx
						SELECT p_code, v_yp_name, '', '2', '一般提示', 'RLT049', 'yyys', '用药医生', '用药医生问题',
								CONCAT(v_yp_name, '不能该医生使用！'), '0', '用药科室';
				END IF;
		END IF;

		-- 给药途径检查（取药用、遵医嘱、自用拦截）
		SELECT COUNT(1) INTO v_n_count9
		FROM rms_t_pres_med
		WHERE code = p_code
		AND administer IN (
				SELECT ADM_CODE
				FROM rms_itf_hos_admin_route
				WHERE jzbs = '1'
		);

		IF v_n_count9 > 0 THEN
				INSERT INTO rms_t_pres_fx
				SELECT p_code, v_yp_name, '', '2', '一般提示', 'RLT003', 'GYTJJJ', '给药途径错误', '给药途径错误',
						'所有药品不能使用【取药用、遵医嘱、自用】给药途径！', '0', '给药途径错误';
		END IF;

		-- 自定义给药途径分析
		SELECT COUNT(1) INTO v_n_count12
		FROM rms_t_med_zdy_gytj
		WHERE yp_code = p_yp_code;

		SELECT COUNT(1) INTO v_n_count13
		FROM rms_t_pres_med
		WHERE code = p_code AND his_code = p_yp_code
		AND administer NOT IN (
				SELECT gytj_code
				FROM rms_t_med_zdy_gytj
				WHERE yp_code = p_yp_code
		);

		IF v_n_count12 > 0 AND v_n_count13 > 0 THEN
				INSERT INTO rms_t_pres_fx
				SELECT p_code, v_yp_name, '', '1', '一般提示', 'RLT026', 'GYTJWT', '给药途径错误', '给药途径问题',
						CONCAT(v_yp_name, '不符合医院规定给药途径！'), '0', '给药途径';
		END IF;

		-- 住院临时医嘱频次分析
		SELECT COUNT(1) INTO v_n_count18
		FROM rms_t_pres_med a, rms_t_pres b
		WHERE a.code = p_code AND a.Code = b.code
		AND freq IN ('01','13','15','14') AND his_code = p_yp_code;

		SELECT DRUG_NAME INTO v_tymc
		FROM rms_itf_hos_drug
		WHERE DRUG_CODE = p_yp_code LIMIT 1;

		IF v_hosp_flag = 'ip' AND v_pres_type2 = 'T' AND v_n_count18 >= 1 THEN
				DELETE FROM rms_t_pres_fx
				WHERE Code = p_code AND wtcode = 'RLT030'
				AND title LIKE '%频次%' AND ywa = v_tymc;

				SELECT COUNT(1) INTO v_n_count19
				FROM rms_t_pres_fx a, rms_t_pres b
				WHERE a.Code = b.code AND a.code = p_code;

				IF v_n_count19 = 0 THEN
						UPDATE rms_t_pres SET flag = 0 WHERE code = p_code;
				END IF;
		END IF;

		-- 门诊注射剂"立即使用"频次分析
		SELECT COUNT(1) INTO v_n_count20
		FROM rms_t_pres_med a
		WHERE a.code = p_code AND freq IN ('01','13','15','14')
		AND his_code = p_yp_code
		AND (a.med_name LIKE '%针%' OR a.med_name LIKE '%注射%');

		IF v_hosp_flag = 'op' AND v_n_count20 >= 1 THEN
				DELETE FROM rms_t_pres_fx
				WHERE Code = p_code AND wtcode = 'RLT030'
				AND title LIKE '%频次%' AND ywa = v_tymc;
		END IF;

		-- 门诊抗菌药物注射剂允许低于正常频次
		SELECT COUNT(1) INTO v_n_count22
		FROM rms_itf_hos_drug b
		WHERE b.IS_ANTIBAC = '1' AND drug_code = p_yp_code
		AND b.DRUG_PRODUCT_NAME LIKE '%注射%';

		SELECT b.DAILY_TIMES INTO v_DAILY_TIMES
		FROM rms_t_pres_med a, rms_itf_hos_frequency b
		WHERE code = p_code AND a.his_code = p_yp_code
		AND a.freq = b.FREQ_CODE LIMIT 1;

		SELECT MAX(yl_min) INTO v_yl_min
		FROM rms_t_sda_cgl_result a, rms_t_byyydzb b
		WHERE a.sda_id = b.sda_id
		AND b.yp_code = p_yp_code
		AND a.reco_type = '2';

		IF v_hosp_flag = 'op' AND v_n_count22 >= 1 AND v_DAILY_TIMES < v_yl_min THEN
				DELETE FROM rms_t_pres_fx
				WHERE Code = p_code AND wtcode = 'RLT030'
				AND title LIKE '%频次%' AND ywa = v_tymc;

				SELECT COUNT(1) INTO v_n_count23
				FROM rms_t_pres_fx a, rms_t_pres b
				WHERE a.Code = b.code AND a.code = p_code;

				IF v_n_count21 = 0 THEN
						UPDATE rms_t_pres SET flag = 0 WHERE code = p_code;
				END IF;
		END IF;

		-- 历史处方相互作用检查
		IF v_hosp_flag = 'ip' OR v_n_count6 > 0 THEN
				LEAVE main_block;
		END IF;

		SELECT card_code, pres_time, pres_id INTO v_card_code, v_his_time, v_pres_id
		FROM rms_t_pres
		WHERE code = p_code LIMIT 1;

		SELECT COUNT(1) INTO v_n_count24
		FROM rms_t_drug_zdfyl
		WHERE DRUG_CODE = p_yp_code;

		-- 创建临时表进行历史相互作用分析
		DROP TEMPORARY TABLE IF EXISTS temp_xhzy_ls;
		CREATE TEMPORARY TABLE temp_xhzy_ls (
				drug_name VARCHAR(50),
				dept_name VARCHAR(50),
				doct_name VARCHAR(50),
				mid VARCHAR(50)
		);

		INSERT INTO temp_xhzy_ls
		SELECT b.med_name, d.SPEC_NAME, a.doct_name, c.CID
		FROM rms_t_pres a, rms_t_pres_med b, rms_t_byyydzb c, rms_itf_hos_spec d
		WHERE a.code = b.Code
		AND b.his_code = c.yp_code
		AND a.dept_code = d.SPEC_CODE
		AND pres_time > DATE_SUB(CURDATE(), INTERVAL 1 DAY)
		AND hosp_flag = 'op'
		AND flag > -1
		AND flag <> 9
		AND a.prescription_type = '1'
		AND a.card_code = v_card_code
		AND a.pres_id <> v_pres_id;

		-- 插入相互作用警告
		INSERT INTO rms_t_pres_fx (
				Code, ywa, ywb, wtlvlcode, wtlvl, wtcode, wtsp, wtname, title, detail, flag, text
		)
		SELECT p_code, v_yp_name, b.drug_name, '1', '一般提示', 'RLT014', 'XHZYTS', '相互作用',
				CONCAT('【', v_yp_name, '】和【', CURDATE(), '】在', b.dept_name, '的【', b.doct_name, '】开的【', b.drug_name, '】存在相互作用'),
				CONCAT('结果: ', IFNULL(CAST(a.effect AS CHAR(2000)), ''), '; 机制：', IFNULL(CAST(mechanism AS CHAR(4000)), ''), ';', IFNULL(CONCAT('参考文献:', CAST(reference AS CHAR(4000))), '')),
				0, '配伍问题'
		FROM rms_t_xhzy_edi a, temp_xhzy_ls b
		WHERE yaowuA = v_mid AND yaowuB = b.mid
		AND sugflag = 'xhzy'
		UNION
		SELECT p_code, v_yp_name, b.drug_name, '1', '一般提示', 'RLT014', 'XHZYTS', '相互作用',
				CONCAT('【', v_yp_name, '】和【', CURDATE(), '】在', b.dept_name, '的【', b.doct_name, '】开的【', b.drug_name, '】存在相互作用'),
				CONCAT('结果: ', IFNULL(CAST(a.effect AS CHAR(2000)), ''), '; 机制：', IFNULL(CAST(mechanism AS CHAR(4000)), ''), ';', IFNULL(CONCAT('参考文献:', CAST(reference AS CHAR(4000))), '')),
				0, '配伍问题'
		FROM rms_t_xhzy_edi a, temp_xhzy_ls b
		WHERE yaowuA = b.mid AND yaowuB = v_mid
		AND sugflag = 'xhzy';

		DROP TEMPORARY TABLE temp_xhzy_ls;

END